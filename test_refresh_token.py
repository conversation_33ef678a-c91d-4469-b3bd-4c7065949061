#!/usr/bin/env python3
"""
Test script to demonstrate refresh token functionality
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8000/v2"  # Adjust based on your server
CLIENT_ID = "your_tenant_slug"  # Replace with actual tenant slug
USERNAME = "your_username"  # Replace with actual username
PASSWORD = "your_password"  # Replace with actual password

def test_login_with_refresh_token():
    """Test login endpoint that returns both access and refresh tokens"""
    print("=== Testing Login with Refresh Token ===")
    
    login_data = {
        "username": USERNAME,
        "password": PASSWORD,
        "client_id": CLIENT_ID,
        "grant_type": "password"
    }
    
    response = requests.post(f"{BASE_URL}/login", data=login_data)
    
    if response.status_code == 200:
        tokens = response.json()
        print("✅ Login successful!")
        print(f"Access Token: {tokens['access_token'][:50]}...")
        print(f"Refresh Token: {tokens['refresh_token'][:50]}...")
        print(f"Token Type: {tokens['token_type']}")
        print(f"Expires In: {tokens['expires_in']} seconds")
        return tokens
    else:
        print(f"❌ Login failed: {response.status_code}")
        print(response.text)
        return None

def test_verify_token(access_token):
    """Test token verification with access token"""
    print("\n=== Testing Token Verification ===")
    
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(f"{BASE_URL}/verify-token", headers=headers)
    
    if response.status_code == 200:
        user_info = response.json()
        print("✅ Token verification successful!")
        print(f"User: {user_info['user']['username']}")
        print(f"Role: {user_info['user']['role']['name']}")
        return True
    else:
        print(f"❌ Token verification failed: {response.status_code}")
        print(response.text)
        return False

def test_refresh_access_token(refresh_token, access_token):
    """Test refresh token endpoint"""
    print("\n=== Testing Access Token Refresh ===")

    refresh_data = {
        "refresh_token": refresh_token,
        "access_token": access_token
    }

    response = requests.post(f"{BASE_URL}/refresh", json=refresh_data)

    if response.status_code == 200:
        new_tokens = response.json()
        print("✅ Token refresh successful!")
        print(f"New Access Token: {new_tokens['access_token'][:50]}...")
        print(f"Token Type: {new_tokens['token_type']}")
        print(f"Expires In: {new_tokens['expires_in']} seconds")
        return new_tokens['access_token']
    else:
        print(f"❌ Token refresh failed: {response.status_code}")
        print(response.text)
        return None

def test_logout(refresh_token, access_token):
    """Test logout endpoint that revokes refresh token"""
    print("\n=== Testing Logout (Token Revocation) ===")

    logout_data = {
        "refresh_token": refresh_token,
        "access_token": access_token
    }

    response = requests.post(f"{BASE_URL}/logout", json=logout_data)

    if response.status_code == 200:
        result = response.json()
        print("✅ Logout successful!")
        print(f"Message: {result['message']}")
        return True
    else:
        print(f"❌ Logout failed: {response.status_code}")
        print(response.text)
        return False

def test_refresh_after_logout(refresh_token, access_token):
    """Test that refresh token doesn't work after logout"""
    print("\n=== Testing Refresh Token After Logout ===")

    refresh_data = {
        "refresh_token": refresh_token,
        "access_token": access_token
    }

    response = requests.post(f"{BASE_URL}/refresh", json=refresh_data)

    if response.status_code == 401:
        print("✅ Refresh token correctly rejected after logout!")
        return True
    else:
        print(f"❌ Unexpected response: {response.status_code}")
        print(response.text)
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Refresh Token Tests")
    print(f"Base URL: {BASE_URL}")
    print(f"Client ID: {CLIENT_ID}")
    print(f"Username: {USERNAME}")
    
    # Step 1: Login and get tokens
    tokens = test_login_with_refresh_token()
    if not tokens:
        print("❌ Cannot proceed without valid tokens")
        return
    
    access_token = tokens['access_token']
    refresh_token = tokens['refresh_token']
    
    # Step 2: Verify access token works
    if not test_verify_token(access_token):
        print("❌ Access token verification failed")
        return
    
    # Step 3: Wait a moment and refresh the access token
    print("\n⏳ Waiting 2 seconds before refresh...")
    time.sleep(2)

    new_access_token = test_refresh_access_token(refresh_token, access_token)
    if not new_access_token:
        print("❌ Token refresh failed")
        return

    # Step 4: Verify new access token works
    if not test_verify_token(new_access_token):
        print("❌ New access token verification failed")
        return

    # Step 5: Logout (revoke refresh token)
    if not test_logout(refresh_token, access_token):
        print("❌ Logout failed")
        return

    # Step 6: Try to refresh after logout (should fail)
    test_refresh_after_logout(refresh_token, access_token)
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    main()
