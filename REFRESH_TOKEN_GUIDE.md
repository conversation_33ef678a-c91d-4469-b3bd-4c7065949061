# Refresh Token Implementation Guide

This guide explains how to use the refresh token functionality that has been added to the Voice Processing API's user management system.

## Overview

The refresh token system provides enhanced security by using short-lived access tokens (15 minutes) paired with longer-lived refresh tokens (7 days). This approach minimizes the risk of token compromise while maintaining a good user experience.

## Key Features

- **Short-lived Access Tokens**: 15 minutes expiration for enhanced security
- **Long-lived Refresh Tokens**: 7 days expiration for user convenience
- **Token Revocation**: Ability to revoke refresh tokens on logout
- **Database Storage**: Refresh tokens are stored and validated against the database
- **Automatic Cleanup**: Revoked tokens are marked in the database

## API Endpoints

### 1. Login Endpoint

**POST** `/v2/login`

Returns both access and refresh tokens upon successful authentication.

**Request:**
```bash
curl -X POST "http://localhost:8000/v2/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=your_username&password=your_password&client_id=your_tenant_slug&grant_type=password"
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 900
}
```

### 2. Refresh Token Endpoint

**POST** `/v2/refresh`

Exchanges a valid refresh token for a new access token.

**Request:**
```bash
curl -X POST "http://localhost:8000/v2/refresh" \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 900
}
```

### 3. Logout Endpoint

**POST** `/v2/logout`

Revokes the refresh token, effectively logging out the user.

**Request:**
```bash
curl -X POST "http://localhost:8000/v2/logout" \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

**Response:**
```json
{
  "message": "Successfully logged out"
}
```

### 4. Token Verification Endpoint

**GET** `/v2/verify-token`

Verifies if the provided access token is valid.

**Request:**
```bash
curl -X GET "http://localhost:8000/v2/verify-token" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## Implementation Details

### Token Structure

**Access Token:**
- Contains user information (username, role, tenant_id)
- Short expiration (15 minutes)
- Used for API authentication

**Refresh Token:**
- Contains user information plus unique identifier (JTI)
- Long expiration (7 days)
- Stored in database for validation
- Used only for obtaining new access tokens

### Database Schema

The `refresh_tokens` collection stores:
```json
{
  "jti": "unique-token-id",
  "username": "user123",
  "created_at": "2024-01-01T00:00:00Z",
  "expires_at": "2024-01-08T00:00:00Z",
  "revoked": false,
  "revoked_at": null
}
```

### Security Considerations

1. **Token Storage**: Store refresh tokens securely on the client side
2. **HTTPS Only**: Always use HTTPS in production
3. **Token Rotation**: Consider implementing refresh token rotation for enhanced security
4. **Cleanup**: Implement periodic cleanup of expired tokens

## Client Implementation Example

### JavaScript/TypeScript

```javascript
class AuthService {
  constructor(baseUrl, clientId) {
    this.baseUrl = baseUrl;
    this.clientId = clientId;
    this.accessToken = localStorage.getItem('access_token');
    this.refreshToken = localStorage.getItem('refresh_token');
  }

  async login(username, password) {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);
    formData.append('client_id', this.clientId);
    formData.append('grant_type', 'password');

    const response = await fetch(`${this.baseUrl}/login`, {
      method: 'POST',
      body: formData
    });

    if (response.ok) {
      const tokens = await response.json();
      this.accessToken = tokens.access_token;
      this.refreshToken = tokens.refresh_token;
      
      localStorage.setItem('access_token', this.accessToken);
      localStorage.setItem('refresh_token', this.refreshToken);
      
      return tokens;
    }
    throw new Error('Login failed');
  }

  async refreshAccessToken() {
    if (!this.refreshToken || !this.accessToken) {
      throw new Error('No refresh token or access token available');
    }

    const response = await fetch(`${this.baseUrl}/refresh`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        refresh_token: this.refreshToken,
        access_token: this.accessToken
      })
    });

    if (response.ok) {
      const tokens = await response.json();
      this.accessToken = tokens.access_token;
      localStorage.setItem('access_token', this.accessToken);
      return tokens;
    }
    throw new Error('Token refresh failed');
  }

  async logout() {
    if (this.refreshToken && this.accessToken) {
      await fetch(`${this.baseUrl}/logout`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          refresh_token: this.refreshToken,
          access_token: this.accessToken
        })
      });
    }

    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  async makeAuthenticatedRequest(url, options = {}) {
    let response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${this.accessToken}`
      }
    });

    // If token expired, try to refresh
    if (response.status === 401) {
      await this.refreshAccessToken();
      response = await fetch(url, {
        ...options,
        headers: {
          ...options.headers,
          'Authorization': `Bearer ${this.accessToken}`
        }
      });
    }

    return response;
  }
}
```

## Testing

Use the provided `test_refresh_token.py` script to test the functionality:

```bash
# Update the configuration in the script
python test_refresh_token.py
```

## Migration Notes

If you're upgrading from the previous token system:

1. **Database Migration**: The `refresh_tokens` collection will be automatically created for new tenants
2. **Client Updates**: Update client applications to handle the new token response format
3. **Token Expiration**: Access tokens now expire in 15 minutes instead of 1 day

## Troubleshooting

### Common Issues

1. **"Invalid refresh token"**: Check if the token has been revoked or expired
2. **"User not found"**: Verify the client_id corresponds to the correct tenant
3. **Database errors**: Ensure the `refresh_tokens` collection exists in the tenant database

### Debugging

Enable debug logging to see detailed token operations:

```python
import logging
logging.getLogger("app.v2.user_management").setLevel(logging.DEBUG)
```
